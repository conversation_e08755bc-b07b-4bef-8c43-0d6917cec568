<?php

declare(strict_types=1);

namespace LiveStream\Platforms\Douyin;

use LiveStream\Enum\Quality;
use LiveStream\Contracts\LiveInterface;
use LiveStream\Stream\Stream;
use LiveStream\Enum\StreamType;

class Live implements LiveInterface
{
    public function __construct(
        protected int $status,
        protected string $title,
        protected string $anchorName,
        protected string $roomId,
        protected array $streamUrl,
        protected string $userCountStr
    ) {}

    public function isLive(): bool
    {
        return $this->status === 2;
    }

    public function getFlvUrl(?Quality $quality = null): ?Stream
    {
        // 如果指定了具体的清晰度，直接返回对应的URL
        if ($quality !== null) {
            $url = $this->streamUrl[StreamType::FLV->value][$quality->value] ?? null;
            return $url ? Stream::createFlv($url, $quality) : null;
        }

        // 如果quality为null，使用智能降级逻辑
        return $this->findAvailableUrl(StreamType::FLV);
    }

    public function getHlsUrl(?Quality $quality = null): ?Stream
    {
        // 如果指定了具体的清晰度，直接返回对应的URL
        if ($quality !== null) {
            $url = $this->streamUrl[StreamType::HLS->value][$quality->value] ?? null;
            return $url ? Stream::createHls($url, $quality) : null;
        }

        // 如果quality为null，使用智能降级逻辑
        $url = $this->findAvailableUrl(StreamType::HLS);
        if ($url === null) {
            return null;
        }

        // 确定选择的清晰度
        $selectedQuality = $this->determineQualityFromUrl($url, StreamType::HLS->value);
        return Stream::createHls($url, $selectedQuality);
    }

    public function getStreamUrl(): array
    {
        return $this->streamUrl;
    }

    public function getAnchorName(): string
    {
        return $this->anchorName;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getRoomId(): string
    {
        return $this->roomId;
    }

    public function getUserCountStr(): string
    {
        return $this->userCountStr;
    }

    /**
     * 智能降级：按优先级顺序查找可用的流URL
     *
     * @param StreamType $streamType URL类型：'flv' 或 'hls'
     * @return string|null 找到的第一个可用URL，如果都不可用则返回null
     */
    public function findAvailableUrl(StreamType $streamType): ?Stream
    {
        // 检查streamUrl结构是否有效
        if (!isset($this->streamUrl[$streamType->value]) || !is_array($this->streamUrl[$streamType->value])) {
            return null;
        }

        $urlMap = $this->streamUrl[$streamType->value];

        // 按优先级顺序尝试每个清晰度
        foreach (Quality::getPriorityOrder() as $quality) {
            $url = $urlMap[$quality->value] ?? null;

            // 检查URL是否存在且不为空字符串
            if ($url !== null && $url !== '') {

                if ($streamType->value === StreamType::FLV->value) {
                    return Stream::createFlv($url, $quality);
                } else if ($streamType->value === StreamType::HLS->value) {
                    return Stream::createHls($url, $quality);
                }
            }
        }

        return null;
    }

    /**
     * 根据URL确定清晰度
     *
     * @param string $url 流URL
     * @param string $urlType URL类型
     * @return Quality|null 确定的清晰度
     */
    private function determineQualityFromUrl(string $url, string $urlType): ?Quality
    {
        if (!isset($this->streamUrl[$urlType]) || !is_array($this->streamUrl[$urlType])) {
            return null;
        }

        $urlMap = $this->streamUrl[$urlType];

        // 按优先级顺序查找匹配的清晰度
        foreach (Quality::getPriorityOrder() as $quality) {
            if (isset($urlMap[$quality->value]) && $urlMap[$quality->value] === $url) {
                return $quality;
            }
        }

        return null;
    }

    /**
     * 向后兼容方法：获取FLV URL字符串
     *
     * @param Quality|null $quality 清晰度
     * @return string|null FLV URL字符串
     */
    public function getFlvUrlString(?Quality $quality = null): ?string
    {
        $stream = $this->getFlvUrl($quality);
        return $stream?->getUrl();
    }

    /**
     * 向后兼容方法：获取HLS URL字符串
     *
     * @param Quality|null $quality 清晰度
     * @return string|null HLS URL字符串
     */
    public function getHlsUrlString(?Quality $quality = null): ?string
    {
        $stream = $this->getHlsUrl($quality);
        return $stream?->getUrl();
    }
}
