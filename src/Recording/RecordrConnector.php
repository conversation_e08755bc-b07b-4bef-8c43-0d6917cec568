<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use Closure;
use LiveStream\Traits\HasMiddleware;
use LiveStream\Traits\HasRecordr;
use LiveStream\Traits\HasConfig;
use LiveStream\Traits\HasRetry;
use LiveStream\Contracts\PlatformInterface;
use LiveStream\Recording\Pipes\ValidateOptionsPipe;
use LiveStream\Recording\Pipes\StreamValidationPipe;
use LogicException;

class RecordrConnector
{
    use HasMiddleware;
    use HasRecordr;
    use HasConfig;
    use HasRetry;

    public function handle(PlatformInterface $platform, ?Closure $progress = null): mixed
    {
        $attempts = 0;
        $maxTries = $this->getTries();
        $retryInterval = $this->getRetryInterval();
        $useExponentialBackoff = $this->hasExponentialBackoff();

        // 如果没有配置重试，设置为1次
        if ($maxTries <= 0) {
            $maxTries = 1;
        }

        // 添加默认的验证中间件
        $this->middleware()->pipe(new ValidateOptionsPipe());

        // 添加流验证中间件
        $this->middleware()->pipe(new StreamValidationPipe());

        while ($attempts < $maxTries) {
            $attempts++;

            // 如果不是第一次尝试，执行延迟
            if ($attempts > 1) {
                $sleepTime = $useExponentialBackoff
                    ? $retryInterval * (2 ** ($attempts - 2)) * 1000
                    : $retryInterval * 1000;

                if ($sleepTime > 0) {
                    usleep($sleepTime);
                }
            }

            try {
                // 每次重试都创建新的 PendingRecorder
                $pendingRecorder = $this->createPendingRecorder($platform);

                // 执行中间件管道
                $pendingRecorder = $this->middleware()
                    ->send($pendingRecorder)
                    ->thenReturn();

                // 执行录制
                return $pendingRecorder->recordrConnector()->recordr()->start(
                    pendingRecorder: $pendingRecorder,
                    progress: $progress
                );
            } catch (\Throwable $exception) {

                // 如果已经是最后一次尝试，抛出异常
                if ($attempts >= $maxTries) {
                    throw $exception;
                }

                // 检查是否应该重试
                if (!$this->shouldRetry($exception, $attempts)) {
                    throw $exception;
                }
            }
        }

        // 理论上不应该到达这里
        throw new LogicException('The recording was not started.');
    }

    /**
     * 创建 PendingRecorder 实例
     */
    protected function createPendingRecorder(PlatformInterface $platform): PendingRecorder
    {
        return new PendingRecorder($this, $platform);
    }
}
