<?php

declare(strict_types=1);

namespace LiveStream\Recording\Pipes;

use Closure;
use LiveStream\Recording\PendingRecorder;

/**
 * 调试信息中间件
 * 
 * 在流验证完成后显示详细的录制和流信息
 */
class DebugInfoPipe
{
    private static int $attemptCounter = 0;

    /**
     * 处理调试信息显示
     *
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param Closure $next 下一个中间件
     * @return mixed
     */
    public function handle(PendingRecorder $pendingRecorder, Closure $next)
    {
        self::$attemptCounter++;
        
        // 先执行下一个中间件，确保所有验证完成
        $result = $next($pendingRecorder);

        if(!$pendingRecorder->recordrConnector()->getDebug()){
            return $result;
        }

        // 显示基本录制信息
        $this->displayRecordingInfo($pendingRecorder, self::$attemptCounter);
        
        
        // 现在可以安全地显示流信息
        $this->displayStreamInfo($pendingRecorder, self::$attemptCounter);
        
        // 显示调试信息
        $this->displayDebugInfo($pendingRecorder);
        
        return $result;
    }

    /**
     * 显示录制信息
     */
    private function displayRecordingInfo(PendingRecorder $pendingRecorder, int $attempt): void
    {
        echo "\n=== 录制信息 (第 {$attempt} 次尝试) ===\n";
        echo "录制ID: " . $pendingRecorder->getLive()->getRoomId() . "\n";
        echo "主播: " . $pendingRecorder->getLive()->getAnchorName() . "\n";
        echo "标题: " . $pendingRecorder->getLive()->getTitle() . "\n";
        echo "输出路径: " . $pendingRecorder->savePath() . "\n";
    }

    /**
     * 显示流信息
     */
    private function displayStreamInfo(PendingRecorder $pendingRecorder, int $attempt): void
    {
        echo "\n=== 直播流信息 (第 {$attempt} 次尝试) ===\n";
        
        $stream = $pendingRecorder->getStream();
        if ($stream !== null) {
            echo "流类型: " . $stream->getType() . "\n";
            echo "流地址: " . $this->truncateUrl($stream->getUrl()) . "\n";
            echo "流质量: " . ($stream->getQuality()?->value ?? 'unknown') . "\n";
            echo "流哈希: " . $stream->getHash() . "\n";
            echo "已验证: " . ($stream->isValidated() ? '是' : '否') . "\n";
            
            // 显示过期信息（如果有）
            if ($stream->getExpire()) {
                echo "过期时间: " . $stream->getExpire()->toDateTimeString() . "\n";
                echo "是否已过期: " . ($stream->isExpired() ? '是' : '否') . "\n";
                echo "剩余时间: " . $stream->getTimeToExpire() . " 秒\n";
            } else {
                echo "过期时间: 无\n";
            }
        } else {
            echo "流信息: 尚未获取或验证失败\n";
        }
        
        // 显示原始流URL数据
        $streamUrls = $pendingRecorder->getLive()->getStreamUrl();
        echo "可用流数量: " . $this->countAvailableStreams($streamUrls) . "\n";
    }

    /**
     * 显示调试信息
     */
    private function displayDebugInfo(PendingRecorder $pendingRecorder): void
    {
        echo "\n=== 调试信息 ===\n";
        echo "直播状态: " . ($pendingRecorder->getLive()->isLive() ? '直播中' : '未直播') . "\n";
        echo "配置的流类型: " . $pendingRecorder->getConfig()->getStreamType()->value . "\n";
        echo "配置的质量: " . ($pendingRecorder->getConfig()->getQuality()?->value ?? 'auto') . "\n";
        echo "配置的格式: " . $pendingRecorder->getConfig()->getFormat()->value . "\n";
        
        // 显示重试配置
        $connector = $pendingRecorder->recordrConnector();
        echo "最大重试次数: " . $connector->getTries() . "\n";
        echo "重试间隔: " . $connector->getRetryInterval() . "ms\n";
        echo "指数退避: " . ($connector->hasExponentialBackoff() ? '启用' : '禁用') . "\n";
    }

    /**
     * 截断URL以便显示
     */
    private function truncateUrl(string $url, int $maxLength = 80): string
    {
        if (strlen($url) <= $maxLength) {
            return $url;
        }
        
        return substr($url, 0, $maxLength) . '...';
    }

    /**
     * 计算可用流的数量
     */
    private function countAvailableStreams(array $streamUrls): int
    {
        $count = 0;
        foreach ($streamUrls as $type => $qualities) {
            if (is_array($qualities)) {
                foreach ($qualities as $quality => $url) {
                    if (!empty($url)) {
                        $count++;
                    }
                }
            }
        }
        return $count;
    }

    /**
     * 重置尝试计数器（用于测试）
     */
    public static function resetAttemptCounter(): void
    {
        self::$attemptCounter = 0;
    }
}
