<?php

declare(strict_types=1);

namespace LiveStream\Recording\Pipes;

use Closure;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Exceptions\StreamUnavailableException;


class StreamValidationPipe
{
    /**
     * 处理流验证
     *
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param Closure $next 下一个中间件
     * @return mixed
     * @throws StreamUnavailableException 当流不可用时
     */
    public function handle(PendingRecorder $pendingRecorder, Closure $next)
    {
        $streamTypes = $pendingRecorder->getConfig()->getStreamType();
        $live = $pendingRecorder->getLive();

        // 检查直播状态
        if (!$live->isLive()) {
            throw new StreamUnavailableException('直播已结束或未开始');
        }

        // 获取可用的流
        $stream = $live->findAvailableUrl($streamTypes);

        if ($stream === null) {
            $availableStreams = $this->getAvailableStreamInfo($live);
            throw new StreamUnavailableException(
                "未找到 {$streamTypes->value} 类型的流。可用流: " . $availableStreams
            );
        }

        if (!$stream->validateUrl()) {
            throw new StreamUnavailableException(
                "URL格式无效: {$stream->getUrl()}"
            );
        }

        if (!$stream->validateFormat()) {
            throw new StreamUnavailableException(
                "流格式不匹配: {$stream->getUrl()}"
            );
        }

        if (!$stream->validateConnection()) {
            throw new StreamUnavailableException(
                "网络连接失败: {$stream->getUrl()}"
            );
        }

        // 检查流是否已过期
        if ($stream->getExpire() && $stream->isExpired()) {
            throw new StreamUnavailableException(
                "流已过期，过期时间: " . $stream->getExpire()->toDateTimeString()
            );
        }

        $pendingRecorder->withStream($stream);

        return $next($pendingRecorder);
    }

    /**
     * 获取可用流信息
     */
    private function getAvailableStreamInfo($live): string
    {
        $streamUrls = $live->getStreamUrl();
        $info = [];

        foreach ($streamUrls as $type => $qualities) {
            if (is_array($qualities)) {
                $count = count(array_filter($qualities, fn($url) => !empty($url)));
                if ($count > 0) {
                    $info[] = "{$type}({$count})";
                }
            }
        }

        return empty($info) ? '无' : implode(', ', $info);
    }

    /**
     * 获取验证错误详情
     */
    private function getValidationErrorDetails($stream): string
    {
        $errors = [];

        if (!$stream->validateUrl()) {
            $errors[] = 'URL格式无效';
        }

        if (!$stream->validateFormat()) {
            $errors[] = '流格式不匹配';
        }

        if (!$stream->validateConnection()) {
            $errors[] = '网络连接失败';
        }

        return empty($errors) ? '未知错误' : implode(', ', $errors);
    }
}
