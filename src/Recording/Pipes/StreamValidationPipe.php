<?php

declare(strict_types=1);

namespace LiveStream\Recording\Pipes;

use Closure;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Exceptions\StreamUnavailableException;


class StreamValidationPipe
{
    /**
     * 处理流验证
     *
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param Closure $next 下一个中间件
     * @return mixed
     * @throws StreamUnavailableException 当流不可用时
     */
    public function handle(PendingRecorder $pendingRecorder, Closure $next)
    {

        $streamTypes = $pendingRecorder->getConfig()->getStreamType();

        $stream = $pendingRecorder->getLive()->findAvailableUrl($streamTypes);

        if ($stream === null || !$stream->validate()) {
            throw new StreamUnavailableException('No valid stream URL available after validation');
        }

        $pendingRecorder->withStream($stream);

        return $next($pendingRecorder);
    }
}
