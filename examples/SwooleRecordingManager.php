<?php

declare(strict_types=1);

namespace Examples;

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/config/SwooleRecordingConfig.php';

use Examples\Config\SwooleRecordingConfig;
use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;
use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Exceptions\StreamUnavailableException;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

/**
 * Swoole 协程录制管理器
 * 
 * 使用 Swoole 协程并发处理多个直播流录制任务，
 * 提供完善的错误处理、资源管理和监控功能。
 */
class SwooleRecordingManager
{
    private array $config;
    private PlatformManager $platformManager;
    private array $statistics = [
        'total_tasks' => 0,
        'completed_tasks' => 0,
        'failed_tasks' => 0,
        'start_time' => 0,
        'memory_peak' => 0,
    ];

    /**
     * 构造函数
     * 
     * @param array $config 配置数组
     */
    public function __construct(array $config = [])
    {
        $this->config = SwooleRecordingConfig::mergeConfig($config);
        SwooleRecordingConfig::validateConfig($this->config);
        
        // 创建共享的平台管理器
        $this->platformManager = new PlatformManager(new PlatformFactory());
        
        $this->statistics['start_time'] = microtime(true);
        
        $this->log('info', 'SwooleRecordingManager 初始化完成', [
            'max_coroutines' => $this->config['swoole']['max_coroutines'],
            'total_urls' => count($this->config['urls']),
        ]);
    }

    /**
     * 开始录制任务
     * 
     * @return void
     */
    public function startRecording(): void
    {
        $urls = $this->config['urls'];
        $this->statistics['total_tasks'] = count($urls);
        
        $this->log('info', '开始录制任务', [
            'total_urls' => count($urls),
            'max_coroutines' => $this->config['swoole']['max_coroutines'],
        ]);

        // 启动监控协程
        if ($this->config['swoole']['enable_monitoring']) {
            $this->startMonitoring();
        }

        // 使用 Channel 控制并发数量
        $channel = new Channel(count($urls));
        
        // 将所有URL推入Channel
        foreach ($urls as $url) {
            $channel->push($url);
        }

        // 启动工作协程
        for ($i = 0; $i < $this->config['swoole']['max_coroutines']; $i++) {
            $this->startWorkerCoroutine($channel, $i + 1);
        }

        // 等待所有任务完成
        $this->waitForCompletion();
    }

    /**
     * 启动工作协程
     * 
     * @param Channel $channel URL通道
     * @param int $workerId 工作者ID
     * @return void
     */
    private function startWorkerCoroutine(Channel $channel, int $workerId): void
    {
        Coroutine::create(function () use ($channel, $workerId) {
            $this->log('info', "工作协程 #{$workerId} 启动", [
                'coroutine_id' => Coroutine::getCid(),
                'worker_id' => $workerId,
            ]);

            try {
                while (($url = $channel->pop(0.1)) !== false) {
                    if ($url === null) {
                        break; // 超时，继续检查
                    }
                    
                    $this->processUrl($url, $workerId);
                }
            } catch (\Throwable $e) {
                $this->log('error', "工作协程 #{$workerId} 异常", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            } finally {
                $this->log('info', "工作协程 #{$workerId} 结束");
            }
        });
    }

    /**
     * 处理单个URL
     * 
     * @param string $url 直播URL
     * @param int $workerId 工作者ID
     * @return void
     */
    private function processUrl(string $url, int $workerId): void
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $this->log('info', "开始处理URL", [
            'url' => $this->maskUrl($url),
            'worker_id' => $workerId,
            'coroutine_id' => Coroutine::getCid(),
        ]);

        try {
            // 获取平台实例
            $platform = $this->platformManager->driver($url);
            
            // 创建录制配置
            $options = $this->createRecordingOptions();
            
            // 创建录制连接器
            $recordrConnector = $this->createRecordrConnector($options);
            
            // 执行录制
            $result = $recordrConnector->handle($platform, function (string $type, string $buffer) use ($url, $workerId) {
                $this->log('debug', "FFmpeg输出", [
                    'url' => $this->maskUrl($url),
                    'worker_id' => $workerId,
                    'type' => $type,
                    'buffer' => trim($buffer),
                ]);
            });

            $this->statistics['completed_tasks']++;
            
            $endTime = microtime(true);
            $endMemory = memory_get_usage();
            
            $this->log('info', "URL处理完成", [
                'url' => $this->maskUrl($url),
                'worker_id' => $workerId,
                'duration' => round($endTime - $startTime, 2),
                'memory_used' => round(($endMemory - $startMemory) / 1024 / 1024, 2) . 'MB',
            ]);

        } catch (\Throwable $e) {
            $this->statistics['failed_tasks']++;
            
            $this->log('error', "URL处理失败", [
                'url' => $this->maskUrl($url),
                'worker_id' => $workerId,
                'error' => $e->getMessage(),
                'exception_type' => get_class($e),
            ]);
        }
    }

    /**
     * 创建录制选项
     * 
     * @return RecordingOptions
     */
    private function createRecordingOptions(): RecordingOptions
    {
        $options = new RecordingOptions();
        $recordingConfig = $this->config['recording'];
        
        $options->setSavePath($recordingConfig['save_path']);
        $options->setFormat($recordingConfig['format']);
        
        $options->set([
            'timeout' => $recordingConfig['timeout'],
            'max_retries' => $recordingConfig['max_retries'],
            'custom_headers' => $recordingConfig['custom_headers'],
        ]);

        return $options;
    }

    /**
     * 创建录制连接器
     * 
     * @param RecordingOptions $options 录制选项
     * @return RecordrConnector
     */
    private function createRecordrConnector(RecordingOptions $options): RecordrConnector
    {
        $recordrConnector = new RecordrConnector();
        
        $recordrConnector->withConfig($options);
        
        // 使用专业的调试中间件
        if ($this->config['swoole']['enable_debug']) {
            $recordrConnector->middleware()->pipe(new DebugInfoPipe());
        }
        
        $recordrConnector
            ->withTries($this->config['recording']['max_retries'])
            ->withRetryInterval($this->config['recording']['retry_interval'])
            ->withExponentialBackoff($this->config['recording']['exponential_backoff']);

        // 设置重试逻辑
        $recordrConnector->withShouldRetry(function (\Throwable $exception, int $attempt) {
            return $this->shouldRetry($exception, $attempt);
        });

        return $recordrConnector;
    }

    /**
     * 判断是否应该重试
     * 
     * @param \Throwable $exception 异常
     * @param int $attempt 尝试次数
     * @return bool 是否重试
     */
    private function shouldRetry(\Throwable $exception, int $attempt): bool
    {
        $exceptionClass = get_class($exception);
        
        // 不重试的异常类型
        if (in_array($exceptionClass, $this->config['no_retry_exceptions'])) {
            $this->log('info', "异常不重试", [
                'exception' => $exceptionClass,
                'message' => $exception->getMessage(),
                'attempt' => $attempt,
            ]);
            return false;
        }
        
        // 重试的异常类型
        if (in_array($exceptionClass, $this->config['retry_exceptions'])) {
            $this->log('info', "异常重试", [
                'exception' => $exceptionClass,
                'message' => $exception->getMessage(),
                'attempt' => $attempt,
            ]);
            return true;
        }
        
        // 默认不重试
        return false;
    }

    /**
     * 启动监控协程
     * 
     * @return void
     */
    private function startMonitoring(): void
    {
        Coroutine::create(function () {
            $this->log('info', '监控协程启动');
            
            while (true) {
                Coroutine::sleep($this->config['swoole']['gc_interval']);
                
                $currentMemory = memory_get_usage(true);
                $this->statistics['memory_peak'] = max($this->statistics['memory_peak'], $currentMemory);
                
                // 内存清理
                if ($currentMemory > $this->config['swoole']['memory_limit']) {
                    gc_collect_cycles();
                    $this->log('warning', '执行内存清理', [
                        'before_gc' => round($currentMemory / 1024 / 1024, 2) . 'MB',
                        'after_gc' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                    ]);
                }
                
                // 输出统计信息
                $this->logStatistics();
                
                // 检查是否所有任务完成
                if ($this->isAllTasksCompleted()) {
                    break;
                }
            }
            
            $this->log('info', '监控协程结束');
        });
    }

    /**
     * 等待所有任务完成
     * 
     * @return void
     */
    private function waitForCompletion(): void
    {
        while (!$this->isAllTasksCompleted()) {
            Coroutine::sleep(1);
        }
        
        $this->logFinalStatistics();
    }

    /**
     * 检查是否所有任务完成
     * 
     * @return bool
     */
    private function isAllTasksCompleted(): bool
    {
        return ($this->statistics['completed_tasks'] + $this->statistics['failed_tasks']) >= $this->statistics['total_tasks'];
    }

    /**
     * 记录统计信息
     * 
     * @return void
     */
    private function logStatistics(): void
    {
        $runtime = microtime(true) - $this->statistics['start_time'];
        
        $this->log('info', '运行统计', [
            'runtime' => round($runtime, 2) . 's',
            'completed' => $this->statistics['completed_tasks'],
            'failed' => $this->statistics['failed_tasks'],
            'total' => $this->statistics['total_tasks'],
            'memory_current' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
            'memory_peak' => round($this->statistics['memory_peak'] / 1024 / 1024, 2) . 'MB',
            'active_coroutines' => Coroutine::stats()['coroutine_num'] ?? 0,
        ]);
    }

    /**
     * 记录最终统计信息
     * 
     * @return void
     */
    private function logFinalStatistics(): void
    {
        $runtime = microtime(true) - $this->statistics['start_time'];
        
        $this->log('info', '录制任务完成', [
            'total_runtime' => round($runtime, 2) . 's',
            'completed_tasks' => $this->statistics['completed_tasks'],
            'failed_tasks' => $this->statistics['failed_tasks'],
            'total_tasks' => $this->statistics['total_tasks'],
            'success_rate' => round($this->statistics['completed_tasks'] / $this->statistics['total_tasks'] * 100, 2) . '%',
            'memory_peak' => round($this->statistics['memory_peak'] / 1024 / 1024, 2) . 'MB',
        ]);
    }

    /**
     * 记录日志
     * 
     * @param string $level 日志级别
     * @param string $message 消息
     * @param array $context 上下文
     * @return void
     */
    private function log(string $level, string $message, array $context = []): void
    {
        if (!$this->config['logging']['enable']) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $contextStr = '';
        
        if (!empty($context)) {
            if ($this->config['logging']['include_memory_usage']) {
                $context['memory'] = round(memory_get_usage() / 1024 / 1024, 2) . 'MB';
            }
            
            if ($this->config['logging']['include_coroutine_id']) {
                $context['cid'] = Coroutine::getCid();
            }
            
            $contextStr = ' ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        printf(
            $this->config['logging']['format'] . "\n",
            $timestamp,
            strtoupper($level),
            $message . $contextStr
        );
    }

    /**
     * 掩码URL（隐藏敏感信息）
     * 
     * @param string $url 原始URL
     * @return string 掩码后的URL
     */
    private function maskUrl(string $url): string
    {
        $parsed = parse_url($url);
        $host = $parsed['host'] ?? 'unknown';
        $path = $parsed['path'] ?? '';
        
        return $host . $path . '?...';
    }
}
