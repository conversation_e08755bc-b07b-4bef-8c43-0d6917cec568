<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use Swoole\Coroutine;
use Swoole\Coroutine\Channel;

echo "=== Swoole 优化效果演示 ===\n\n";

// 模拟原始版本的问题
function demonstrateOriginalProblems(): void
{
    echo "📋 演示1：原始版本的问题\n";
    echo "问题：每个协程都创建新的实例，没有资源复用\n\n";
    
    $urls = ['url1', 'url2', 'url3'];
    
    foreach ($urls as $url) {
        go(function () use ($url) {
            // ❌ 问题：每个协程都创建新实例
            $instance = new stdClass();
            $instance->id = uniqid();
            
            echo "❌ [协程 " . Coroutine::getCid() . "] 创建新实例: {$instance->id} 处理 {$url}\n";
            Coroutine::sleep(0.1);
        });
    }
    
    // 等待协程完成
    Coroutine::sleep(1);
    echo "\n";
}

// 演示优化版本的改进
function demonstrateOptimizedVersion(): void
{
    echo "📋 演示2：优化版本的改进\n";
    echo "改进：使用共享实例和通道通信\n\n";
    
    $urls = ['url1', 'url2', 'url3'];
    $resultChannel = new Channel(count($urls));
    
    // ✅ 改进：共享实例
    $sharedInstance = new stdClass();
    $sharedInstance->id = uniqid();
    
    echo "✅ 创建共享实例: {$sharedInstance->id}\n";
    
    foreach ($urls as $url) {
        go(function () use ($url, $sharedInstance, $resultChannel) {
            echo "✅ [协程 " . Coroutine::getCid() . "] 使用共享实例: {$sharedInstance->id} 处理 {$url}\n";
            
            // 模拟处理结果
            $result = [
                'cid' => Coroutine::getCid(),
                'url' => $url,
                'status' => 'success',
                'timestamp' => date('H:i:s')
            ];
            
            $resultChannel->push($result);
            Coroutine::sleep(0.1);
        });
    }
    
    // 监控协程执行
    go(function () use ($resultChannel, $urls) {
        $completed = 0;
        while ($completed < count($urls)) {
            $result = $resultChannel->pop(1);
            if ($result !== false) {
                $completed++;
                echo "📊 [监控] 协程 {$result['cid']} 完成 {$result['url']} - {$result['timestamp']}\n";
            }
        }
        echo "🏁 所有任务完成\n";
    });
    
    // 等待所有协程完成
    Coroutine::sleep(1);
    echo "\n";
}

// 演示中间件执行顺序
function demonstrateMiddlewareOrder(): void
{
    echo "📋 演示3：中间件执行顺序\n";
    echo "展示正确的中间件注册和执行顺序\n\n";
    
    class MockMiddleware
    {
        public function __construct(private string $name) {}
        
        public function handle($data, \Closure $next)
        {
            echo "🔄 [中间件 {$this->name}] 前置处理\n";
            $result = $next($data);
            echo "🔙 [中间件 {$this->name}] 后置处理\n";
            return $result;
        }
    }
    
    class MockPipeline
    {
        private array $middlewares = [];
        
        public function pipe($middleware): self
        {
            $this->middlewares[] = $middleware;
            return $this;
        }
        
        public function process($data)
        {
            $pipeline = array_reduce(
                array_reverse($this->middlewares),
                function ($stack, $middleware) {
                    return function ($data) use ($stack, $middleware) {
                        return $middleware->handle($data, $stack);
                    };
                },
                function ($data) {
                    echo "🎯 [核心逻辑] 处理数据: {$data}\n";
                    return $data . ' (已处理)';
                }
            );
            
            return $pipeline($data);
        }
    }
    
    $pipeline = new MockPipeline();
    
    // ✅ 正确的注册顺序：调试中间件在前
    $pipeline->pipe(new MockMiddleware('DebugInfo'));
    $pipeline->pipe(new MockMiddleware('ValidateOptions'));
    $pipeline->pipe(new MockMiddleware('StreamValidation'));
    
    $result = $pipeline->process('测试数据');
    echo "📤 最终结果: {$result}\n\n";
}

// 演示异常处理改进
function demonstrateExceptionHandling(): void
{
    echo "📋 演示4：异常处理改进\n";
    echo "展示完整的异常类型处理\n\n";
    
    $exceptions = [
        new \Exception('通用异常'),
        new \RuntimeException('运行时异常'),
        new \InvalidArgumentException('参数异常'),
    ];
    
    foreach ($exceptions as $index => $exception) {
        go(function () use ($exception, $index) {
            $cid = Coroutine::getCid();
            
            try {
                echo "🔄 [协程 {$cid}] 模拟处理任务 " . ($index + 1) . "\n";
                
                // 模拟抛出异常
                throw $exception;
                
            } catch (\Throwable $e) {
                $exceptionClass = get_class($e);
                echo "❌ [协程 {$cid}] 捕获异常: {$exceptionClass} - {$e->getMessage()}\n";
                
                // ✅ 改进：根据异常类型决定处理策略
                $shouldRetry = match (true) {
                    $e instanceof \RuntimeException => true,
                    $e instanceof \InvalidArgumentException => false,
                    default => false
                };
                
                echo "  " . ($shouldRetry ? "↻ 可重试" : "✗ 不重试") . "\n";
            }
        });
    }
    
    Coroutine::sleep(0.5);
    echo "\n";
}

// 主协程
Coroutine\run(function () {
    echo "🚀 启动 Swoole 协程演示\n\n";
    
    // 配置协程
    Coroutine::set([
        'max_coroutine' => 1000,
        'hook_flags' => SWOOLE_HOOK_ALL,
    ]);
    
    demonstrateOriginalProblems();
    demonstrateOptimizedVersion();
    demonstrateMiddlewareOrder();
    demonstrateExceptionHandling();
    
    echo "🎉 演示完成\n";
    
    echo "\n💡 关键改进点：\n";
    echo "1. ✅ 正确的 Swoole 协程初始化和配置\n";
    echo "2. ✅ 资源复用，避免重复创建实例\n";
    echo "3. ✅ 使用 Channel 进行协程间通信\n";
    echo "4. ✅ 正确的中间件注册顺序\n";
    echo "5. ✅ 完整的异常处理策略\n";
    echo "6. ✅ 协程监控和状态管理\n";
});

echo "\n🏁 Swoole 演示程序退出\n";
