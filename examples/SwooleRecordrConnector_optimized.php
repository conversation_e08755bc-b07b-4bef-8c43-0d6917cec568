<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;
use LiveStream\Recording\Pipes\DebugInfoPipe;
use LiveStream\Exceptions\StreamNotLiveException;
use LiveStream\Exceptions\StreamUnavailableException;
use Swoole\Coroutine;
use Swoole\Coroutine\Channel;
use Swoole\Timer;

echo "=== Swoole 协程录制器启动 ===\n";

// 配置 Swoole 协程设置
Coroutine::set([
    'max_coroutine' => 100000,
    'hook_flags' => SWOOLE_HOOK_ALL,
    'enable_preemptive_scheduler' => true,
    'socket_timeout' => 60,
    'socket_read_timeout' => 60,
    'socket_write_timeout' => 60,
]);

// 抖音直播 URL 列表
$douyinUrls = [
    'https://live.douyin.com/94400148976?anchor_id=3340774829721161&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/849538281989?anchor_id=2190661965854936&cover_type=&enter_from_merge=web_live&enter_method=web_card&follow_status=0&game_name=&is_recommend=1&is_vs=0&live_type=game&request_id=20250907191920C1430502053F71DB714F&room_id=7547284836243786505&stream_type=vertical&title_type=1&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_page=live_category_page&web_live_tab=4_101&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/685348031238?anchor_id=3263826554526468&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/692509034937?anchor_id=697569189038953&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/17763347301?anchor_id=51449269941&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
];

/**
 * 创建录制配置
 */
function createRecordingOptions(): RecordingOptions
{
    $options = new RecordingOptions();
    $options->setSavePath('/app/downloads');
    $options->setFormat(\LiveStream\Enum\OutputFormat::MP4);
    $options->set([
        'timeout' => 0,
        'max_retries' => 0,
        'custom_headers' => [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer' => 'https://www.douyin.com/',
        ],
    ]);
    
    return $options;
}

/**
 * 创建录制连接器
 */
function createRecordrConnector(): RecordrConnector
{
    $recordrConnector = new RecordrConnector();
    
    // 配置重试策略
    $recordrConnector->withTries(3)
        ->withRetryInterval(1000)
        ->withExponentialBackoff(false);
    
    // 启用调试模式
    $recordrConnector->withDebug(true);
    
    // 自定义重试判断逻辑
    $recordrConnector->withShouldRetry(function (\Throwable $exception, int $attempt) {
        $exceptionClass = get_class($exception);
        echo "🔄 [协程 " . Coroutine::getCid() . "] 捕获异常 [尝试 {$attempt}]: {$exceptionClass} - {$exception->getMessage()}\n";
        
        // 根据异常类型决定是否重试
        switch (true) {
            case $exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException:
            case $exception instanceof \FFMpeg\Exception\RuntimeException:
            case $exception instanceof StreamUnavailableException:
                echo "  ↻ 将进行重试\n";
                return true;
                
            case $exception instanceof StreamNotLiveException:
                echo "  ✗ 直播已结束，不重试\n";
                return false;
                
            default:
                echo "  ✗ 未知异常类型，不重试\n";
                return false;
        }
    });
    
    return $recordrConnector;
}

/**
 * 处理单个直播流录制
 */
function handleSingleStream(string $url, PlatformManager $platformManager, Channel $resultChannel): void
{
    $cid = Coroutine::getCid();
    
    try {
        echo "🚀 [协程 {$cid}] 开始处理: " . parse_url($url, PHP_URL_PATH) . "\n";
        
        // 获取平台实例
        $platform = $platformManager->driver($url);
        
        // 创建录制配置
        $options = createRecordingOptions();
        
        // 创建录制连接器
        $recordrConnector = createRecordrConnector();
        $recordrConnector->withConfig($options);
        
        // 添加调试中间件（在系统中间件之前注册）
        $recordrConnector->middleware()->pipe(new DebugInfoPipe());
        
        // 执行录制
        $result = $recordrConnector->handle($platform, function (string $type, string $buffer) use ($cid) {
            $trimmedBuffer = trim($buffer);
            if (!empty($trimmedBuffer)) {
                echo "📺 [协程 {$cid}] [FFmpeg {$type}]: {$trimmedBuffer}\n";
            }
        });
        
        // 发送成功结果
        $resultChannel->push([
            'cid' => $cid,
            'url' => $url,
            'status' => 'success',
            'result' => $result,
            'message' => '录制完成'
        ]);
        
        echo "✅ [协程 {$cid}] 录制任务完成\n";
        
    } catch (\Throwable $e) {
        // 发送失败结果
        $resultChannel->push([
            'cid' => $cid,
            'url' => $url,
            'status' => 'error',
            'error' => get_class($e),
            'message' => $e->getMessage()
        ]);
        
        echo "❌ [协程 {$cid}] 录制任务失败: " . get_class($e) . " - " . $e->getMessage() . "\n";
    }
}

/**
 * 监控协程执行状态
 */
function monitorCoroutines(Channel $resultChannel, int $totalTasks): void
{
    $completedTasks = 0;
    $successCount = 0;
    $errorCount = 0;
    
    echo "📊 开始监控 {$totalTasks} 个录制任务...\n\n";
    
    while ($completedTasks < $totalTasks) {
        $result = $resultChannel->pop(30); // 30秒超时
        
        if ($result === false) {
            echo "⚠️  监控超时，可能有任务卡住\n";
            break;
        }
        
        $completedTasks++;
        
        if ($result['status'] === 'success') {
            $successCount++;
            echo "✅ 任务完成 [{$completedTasks}/{$totalTasks}] - 协程 {$result['cid']}: {$result['message']}\n";
        } else {
            $errorCount++;
            echo "❌ 任务失败 [{$completedTasks}/{$totalTasks}] - 协程 {$result['cid']}: {$result['error']} - {$result['message']}\n";
        }
    }
    
    echo "\n📈 执行统计:\n";
    echo "  总任务数: {$totalTasks}\n";
    echo "  成功: {$successCount}\n";
    echo "  失败: {$errorCount}\n";
    echo "  完成率: " . round(($completedTasks / $totalTasks) * 100, 2) . "%\n";
    echo "  成功率: " . round(($successCount / $totalTasks) * 100, 2) . "%\n";
}

// 主协程逻辑
Coroutine\run(function () use ($douyinUrls) {
    echo "🎬 启动协程调度器，准备处理 " . count($douyinUrls) . " 个直播流\n\n";
    
    // 创建结果通道
    $resultChannel = new Channel(count($douyinUrls));
    
    // 创建共享的 PlatformManager 实例
    $platformManager = new PlatformManager(new PlatformFactory());
    
    // 启动监控协程
    go(function () use ($resultChannel, $douyinUrls) {
        monitorCoroutines($resultChannel, count($douyinUrls));
    });
    
    // 为每个 URL 创建录制协程
    foreach ($douyinUrls as $index => $url) {
        go(function () use ($url, $platformManager, $resultChannel) {
            // 添加随机延迟，避免同时请求
            Coroutine::sleep(mt_rand(1, 5));
            handleSingleStream($url, $platformManager, $resultChannel);
        });
        
        echo "🔧 创建协程 " . ($index + 1) . " 处理: " . parse_url($url, PHP_URL_PATH) . "\n";
    }
    
    echo "\n🎯 所有协程已创建，等待执行完成...\n\n";
});

echo "\n🏁 Swoole 协程录制器已退出\n";
