<?php

require_once __DIR__ . '/../vendor/autoload.php';


use LiveStream\PlatformFactory;
use LiveStream\Platforms\PlatformManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Recording\RecordrConnector;


$douyingUrl = [

    'https://live.douyin.com/94400148976?anchor_id=3340774829721161&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/849538281989?anchor_id=2190661965854936&cover_type=&enter_from_merge=web_live&enter_method=web_card&follow_status=0&game_name=&is_recommend=1&is_vs=0&live_type=game&request_id=20250907191920C1430502053F71DB714F&room_id=7547284836243786505&stream_type=vertical&title_type=1&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_page=live_category_page&web_live_tab=4_101&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/685348031238?anchor_id=3263826554526468&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/692509034937?anchor_id=697569189038953&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/17763347301?anchor_id=51449269941&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/956397742902?anchor_id=94986806151&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/260141232541?anchor_id=1895180553169611&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    'https://live.douyin.com/653177890214?anchor_id=99052136985&follow_status=0&is_vs=0&vs_ep_group_id=&vs_episode_id=&vs_episode_stage=&vs_season_id=&web_live_tab_2=&web_live_tab_3=',
    

];


//构建 swoole 容器

// 根据 $douyingUrl 数组构建协程
foreach ($douyingUrl as $url) {
    go(function () use ($url) {
        $platformManager = new PlatformManager(new PlatformFactory());
        $platform = $platformManager->driver($url);

        // 创建自定义的录制选项，指定保存路径
$options = new RecordingOptions();

$options->setSavePath('/app/downloads');
// $options->setQuality(\LiveStream\Enum\Quality::FULL_HD1);
$options->setFormat(\LiveStream\Enum\OutputFormat::MP4);
$options->set([
    'timeout' => 0,
    'max_retries' => 0,
    'custom_headers' => [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Referer' => 'https://www.douyin.com/',
    ],
]);

$recordrConnector = new RecordrConnector();

$recordrConnector->withConfig($options);
$recordrConnector->withDebug(true);

$recordrConnector->withTries(tries: 3)                    // 最多重试3次
    ->withRetryInterval(milliseconds: 1000)          // 每次重试延迟1秒（1000毫秒）
    ->withExponentialBackoff(enabled: false);    // 启用指数退避

// 自定义重试判断逻辑
$recordrConnector->withShouldRetry(function (\Throwable $exception, int $attempt) {
    echo "捕获到异常 [尝试 {$attempt}]: " . get_class($exception) . " - " . $exception->getMessage() . "\n";

    // 根据异常类型决定是否重试
    if ($exception instanceof \Alchemy\BinaryDriver\Exception\ExecutionFailureException) {
        echo "  -> 重试\n";
        return true;
    }

    if ($exception instanceof \FFMpeg\Exception\RuntimeException) {
        echo "  -> 重试\n";
        return true;
    }

    return false;
});



$result = $recordrConnector->handle($platform, function (string $type, string $buffer) {
    echo "\n[FFmpeg $type]: {$buffer}" . trim($buffer);
});

    });
}


