<?php

declare(strict_types=1);

namespace Examples\Config;

use LiveStream\Enum\OutputFormat;

/**
 * Swoole 录制配置管理类
 * 
 * 统一管理 Swoole 协程录制的所有配置选项
 */
class SwooleRecordingConfig
{
    /**
     * 获取默认配置
     * 
     * @return array 配置数组
     */
    public static function getDefaultConfig(): array
    {
        return [
            'urls' => require __DIR__ . '/douyin_urls.php',
            
            'recording' => [
                'save_path' => '/app/downloads',
                'format' => OutputFormat::MP4,
                'timeout' => 0,
                'max_retries' => 3,
                'retry_interval' => 1000,
                'exponential_backoff' => false,
                'custom_headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Referer' => 'https://www.douyin.com/',
                ],
            ],
            
            'swoole' => [
                'max_coroutines' => 10000, // 最大并发协程数
                'enable_debug' => true,
                'memory_limit' => 100 * 1024 * 1024, // 100MB
                'gc_interval' => 60, // 垃圾回收间隔（秒）
                'enable_monitoring' => true,
            ],
            
            'logging' => [
                'enable' => true,
                'level' => 'info',
                'format' => '[%s] [%s] %s',
                'include_memory_usage' => true,
                'include_coroutine_id' => true,
            ],
            
            'retry_exceptions' => [
                \Alchemy\BinaryDriver\Exception\ExecutionFailureException::class,
                \FFMpeg\Exception\RuntimeException::class,
                \LiveStream\Exceptions\StreamUnavailableException::class,
            ],
            
            'no_retry_exceptions' => [
                \LiveStream\Exceptions\StreamNotLiveException::class,
            ],
        ];
    }

    /**
     * 验证配置
     * 
     * @param array $config 配置数组
     * @return bool 是否有效
     * @throws \InvalidArgumentException 配置无效时抛出异常
     */
    public static function validateConfig(array $config): bool
    {
        // 验证必需的配置项
        $requiredKeys = ['urls', 'recording', 'swoole'];
        
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key])) {
                throw new \InvalidArgumentException("缺少必需的配置项: {$key}");
            }
        }

        // 验证URL数组
        if (empty($config['urls']) || !is_array($config['urls'])) {
            throw new \InvalidArgumentException("URLs 配置必须是非空数组");
        }

        // 验证录制配置
        if (!isset($config['recording']['save_path'])) {
            throw new \InvalidArgumentException("缺少录制保存路径配置");
        }

        // 验证Swoole配置
        if (!isset($config['swoole']['max_coroutines']) || $config['swoole']['max_coroutines'] <= 0) {
            throw new \InvalidArgumentException("最大协程数必须大于0");
        }

        return true;
    }

    /**
     * 合并用户配置与默认配置
     * 
     * @param array $userConfig 用户配置
     * @return array 合并后的配置
     */
    public static function mergeConfig(array $userConfig = []): array
    {
        $defaultConfig = self::getDefaultConfig();
        
        return array_replace_recursive($defaultConfig, $userConfig);
    }

    /**
     * 获取环境变量配置
     * 
     * @return array 从环境变量读取的配置
     */
    public static function getEnvConfig(): array
    {
        return [
            'recording' => [
                'save_path' => $_ENV['RECORDING_SAVE_PATH'] ?? '/app/downloads',
                'max_retries' => (int)($_ENV['RECORDING_MAX_RETRIES'] ?? 3),
                'retry_interval' => (int)($_ENV['RECORDING_RETRY_INTERVAL'] ?? 1000),
            ],
            'swoole' => [
                'max_coroutines' => (int)($_ENV['SWOOLE_MAX_COROUTINES'] ?? 5),
                'enable_debug' => filter_var($_ENV['SWOOLE_DEBUG'] ?? 'true', FILTER_VALIDATE_BOOLEAN),
                'memory_limit' => (int)($_ENV['SWOOLE_MEMORY_LIMIT'] ?? 100 * 1024 * 1024),
            ],
            'logging' => [
                'enable' => filter_var($_ENV['LOGGING_ENABLE'] ?? 'true', FILTER_VALIDATE_BOOLEAN),
                'level' => $_ENV['LOGGING_LEVEL'] ?? 'info',
            ],
        ];
    }
}
