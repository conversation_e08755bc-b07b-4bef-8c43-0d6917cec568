<?php

declare(strict_types=1);

use LiveStream\Enum\Quality;
use LiveStream\Platforms\Douyin\Live;

describe('Live Quality Degradation', function () {

    beforeEach(function () {
        // 模拟完整的流URL数据结构
        $this->completeStreamUrl = [
            'flv_pull_url' => [
                'ORIGIN' => 'https://example.com/origin.flv',
                'FULL_HD1' => 'https://example.com/fullhd.flv',
                'HD1' => 'https://example.com/hd.flv',
                'SD1' => 'https://example.com/sd.flv',
                'SD2' => 'https://example.com/smooth.flv',
            ],
            'hls_pull_url_map' => [
                'ORIGIN' => 'https://example.com/origin.m3u8',
                'FULL_HD1' => 'https://example.com/fullhd.m3u8',
                'HD1' => 'https://example.com/hd.m3u8',
                'SD1' => 'https://example.com/sd.m3u8',
                'SD2' => 'https://example.com/smooth.m3u8',
            ],
        ];

        // 模拟部分清晰度缺失的流URL数据
        $this->partialStreamUrl = [
            'flv_pull_url' => [
                'HD1' => 'https://example.com/hd.flv',
                'SD1' => 'https://example.com/sd.flv',
                'SD2' => 'https://example.com/smooth.flv',
            ],
            'hls_pull_url_map' => [
                'HD1' => 'https://example.com/hd.m3u8',
                'SD1' => 'https://example.com/sd.m3u8',
                'SD2' => 'https://example.com/smooth.m3u8',
            ],
        ];

        // 模拟只有最低清晰度的流URL数据
        $this->lowestOnlyStreamUrl = [
            'flv_pull_url' => [
                'SD2' => 'https://example.com/smooth.flv',
            ],
            'hls_pull_url_map' => [
                'SD2' => 'https://example.com/smooth.m3u8',
            ],
        ];

        // 模拟空的流URL数据
        $this->emptyStreamUrl = [
            'flv_pull_url' => [],
            'hls_pull_url_map' => [],
        ];
    });

    describe('Quality Priority Order', function () {
        test('Quality enum should provide correct priority order', function () {
            $priorities = Quality::getPriorityOrder();

            expect($priorities)->toBeArray()
                ->and($priorities[0])->toBe(Quality::ORIGIN)
                ->and($priorities[1])->toBe(Quality::FULL_HD1)
                ->and($priorities[2])->toBe(Quality::HD1)
                ->and($priorities[3])->toBe(Quality::SD1)
                ->and($priorities[4])->toBe(Quality::SD2);
        });
    });

    describe('FLV URL Degradation', function () {
        test('should return highest available quality when quality is null', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->completeStreamUrl, '1000');

            $url = $live->getFlvUrlString(null);

            expect($url)->toBe('https://example.com/origin.flv');
        });

        test('should degrade to next available quality when highest is missing', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->partialStreamUrl, '1000');

            $url = $live->getFlvUrlString(null);

            expect($url)->toBe('https://example.com/hd.flv');
        });

        test('should return lowest quality when only lowest is available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->lowestOnlyStreamUrl, '1000');

            $url = $live->getFlvUrlString(null);

            expect($url)->toBe('https://example.com/smooth.flv');
        });

        test('should return null when no quality is available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->emptyStreamUrl, '1000');

            $url = $live->getFlvUrlString(null);

            expect($url)->toBeNull();
        });

        test('should return specific quality when quality is provided and available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->completeStreamUrl, '1000');

            $url = $live->getFlvUrlString(Quality::HD1);

            expect($url)->toBe('https://example.com/hd.flv');
        });

        test('should return null when specific quality is not available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->partialStreamUrl, '1000');

            $url = $live->getFlvUrlString(Quality::ORIGIN);

            expect($url)->toBeNull();
        });
    });

    describe('HLS URL Degradation', function () {
        test('should return highest available quality when quality is null', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->completeStreamUrl, '1000');

            $url = $live->getHlsUrlString(null);

            expect($url)->toBe('https://example.com/origin.m3u8');
        });

        test('should degrade to next available quality when highest is missing', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->partialStreamUrl, '1000');

            $url = $live->getHlsUrlString(null);

            expect($url)->toBe('https://example.com/hd.m3u8');
        });

        test('should return lowest quality when only lowest is available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->lowestOnlyStreamUrl, '1000');

            $url = $live->getHlsUrlString(null);

            expect($url)->toBe('https://example.com/smooth.m3u8');
        });

        test('should return null when no quality is available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->emptyStreamUrl, '1000');

            $url = $live->getHlsUrlString(null);

            expect($url)->toBeNull();
        });

        test('should return specific quality when quality is provided and available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->completeStreamUrl, '1000');

            $url = $live->getHlsUrlString(Quality::HD1);

            expect($url)->toBe('https://example.com/hd.m3u8');
        });

        test('should return null when specific quality is not available', function () {
            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $this->partialStreamUrl, '1000');

            $url = $live->getHlsUrlString(Quality::ORIGIN);

            expect($url)->toBeNull();
        });
    });

    describe('Edge Cases', function () {
        test('should handle malformed stream URL structure gracefully', function () {
            $malformedStreamUrl = [
                'flv_pull_url' => null,
                'hls_pull_url_map' => null,
            ];

            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $malformedStreamUrl, '1000');

            expect($live->getFlvUrl(null))->toBeNull();
            expect($live->getHlsUrl(null))->toBeNull();
        });

        test('should handle empty string URLs gracefully', function () {
            $emptyStringStreamUrl = [
                'flv_pull_url' => [
                    'ORIGIN' => '',
                    'FULL_HD1' => 'https://example.com/fullhd.flv',
                ],
                'hls_pull_url_map' => [
                    'ORIGIN' => '',
                    'FULL_HD1' => 'https://example.com/fullhd.m3u8',
                ],
            ];

            $live = new Live(2, 'Test Title', 'Test Anchor', '123', $emptyStringStreamUrl, '1000');

            // 应该跳过空字符串，返回下一个可用的清晰度
            expect($live->getFlvUrlString(null))->toBe('https://example.com/fullhd.flv');
            expect($live->getHlsUrlString(null))->toBe('https://example.com/fullhd.m3u8');
        });
    });
});
