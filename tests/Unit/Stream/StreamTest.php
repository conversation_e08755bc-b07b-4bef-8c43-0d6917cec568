<?php

declare(strict_types=1);

use LiveStream\Stream\Stream;
use LiveStream\Enum\Quality;

describe('Stream Class', function () {
    
    describe('Basic Properties', function () {
        test('should create stream with basic properties', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->getUrl())->toBe('https://example.com/stream.m3u8');
            expect($stream->getType())->toBe('hls');
            expect($stream->getQuality())->toBe(Quality::HD1);
            expect($stream->isValidated())->toBeFalse();
        });

        test('should create stream with validation status', function () {
            $stream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                Quality::ORIGIN,
                true
            );

            expect($stream->getUrl())->toBe('https://example.com/stream.flv');
            expect($stream->getType())->toBe('flv');
            expect($stream->getQuality())->toBe(Quality::ORIGIN);
            expect($stream->isValidated())->toBeTrue();
        });

        test('should create stream with null quality', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                null
            );

            expect($stream->getQuality())->toBeNull();
        });
    });

    describe('URL Validation', function () {
        test('should validate valid HTTP URLs', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->validateUrl())->toBeTrue();
        });

        test('should reject invalid URLs', function () {
            $stream = new Stream(
                'invalid-url',
                'hls',
                Quality::HD1
            );

            expect($stream->validateUrl())->toBeFalse();
        });

        test('should validate HTTPS URLs', function () {
            $stream = new Stream(
                'https://secure.example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->validateUrl())->toBeTrue();
        });

        test('should validate HTTP URLs', function () {
            $stream = new Stream(
                'http://example.com/stream.flv',
                'flv',
                Quality::SD1
            );

            expect($stream->validateUrl())->toBeTrue();
        });
    });

    describe('Format Validation', function () {
        test('should validate HLS format correctly', function () {
            $hlsStream = new Stream(
                'https://example.com/playlist.m3u8',
                'hls',
                Quality::HD1
            );

            expect($hlsStream->validateFormat())->toBeTrue();
        });

        test('should validate FLV format correctly', function () {
            $flvStream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                Quality::HD1
            );

            expect($flvStream->validateFormat())->toBeTrue();
        });

        test('should reject mismatched format and URL', function () {
            $mismatchedStream = new Stream(
                'https://example.com/stream.flv',
                'hls', // Type says HLS but URL is FLV
                Quality::HD1
            );

            expect($mismatchedStream->validateFormat())->toBeFalse();
        });

        test('should handle URLs without clear extension', function () {
            $noExtStream = new Stream(
                'https://example.com/api/stream?format=m3u8',
                'hls',
                Quality::HD1
            );

            expect($noExtStream->validateFormat())->toBeTrue();
        });
    });

    describe('Connection Validation', function () {
        test('should validate connection for accessible URLs', function () {
            // 注意：这个测试可能会因为网络问题而不稳定
            // 在实际项目中，应该mock HTTP请求
            $stream = new Stream(
                'https://httpbin.org/status/200',
                'hls',
                Quality::HD1
            );

            // 由于网络依赖，我们只测试方法存在且返回布尔值
            $result = $stream->validateConnection();
            expect($result)->toBeIn([true, false]);
        });

        test('should handle connection validation for invalid URLs', function () {
            $stream = new Stream(
                'https://nonexistent-domain-12345.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->validateConnection())->toBeFalse();
        });
    });

    describe('Complete Validation', function () {
        test('should pass complete validation for valid stream', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            // Mock the connection validation to avoid network dependency
            $stream = new class('https://example.com/stream.m3u8', 'hls', Quality::HD1) extends Stream {
                public function validateConnection(): bool {
                    return true; // Mock successful connection
                }
            };

            expect($stream->validate())->toBeTrue();
            expect($stream->isValidated())->toBeTrue();
        });

        test('should fail complete validation for invalid stream', function () {
            $stream = new Stream(
                'invalid-url',
                'hls',
                Quality::HD1
            );

            expect($stream->validate())->toBeFalse();
            expect($stream->isValidated())->toBeFalse();
        });

        test('should update validation status after validation', function () {
            $stream = new class('https://example.com/stream.m3u8', 'hls', Quality::HD1) extends Stream {
                public function validateConnection(): bool {
                    return true;
                }
            };

            expect($stream->isValidated())->toBeFalse();
            $stream->validate();
            expect($stream->isValidated())->toBeTrue();
        });
    });

    describe('Metadata and Serialization', function () {
        test('should convert to array correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1,
                true
            );

            $array = $stream->toArray();

            expect($array)->toBe([
                'url' => 'https://example.com/stream.m3u8',
                'type' => 'hls',
                'quality' => Quality::HD1,
                'validated' => true,
            ]);
        });

        test('should convert to array with null quality', function () {
            $stream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                null,
                false
            );

            $array = $stream->toArray();

            expect($array)->toBe([
                'url' => 'https://example.com/stream.flv',
                'type' => 'flv',
                'quality' => null,
                'validated' => false,
            ]);
        });

        test('should convert to string correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect((string) $stream)->toBe('https://example.com/stream.m3u8');
        });
    });

    describe('Quality Information', function () {
        test('should handle different quality levels', function () {
            $qualities = [
                Quality::ORIGIN,
                Quality::FULL_HD1,
                Quality::HD1,
                Quality::SD1,
                Quality::SD2,
            ];

            foreach ($qualities as $quality) {
                $stream = new Stream(
                    'https://example.com/stream.m3u8',
                    'hls',
                    $quality
                );

                expect($stream->getQuality())->toBe($quality);
            }
        });

        test('should provide quality display information', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->getQualityDisplayName())->toBe(Quality::HD1->getDisplayName());
        });
    });

    describe('Stream Type Information', function () {
        test('should identify HLS streams correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            expect($stream->isHls())->toBeTrue();
            expect($stream->isFlv())->toBeFalse();
        });

        test('should identify FLV streams correctly', function () {
            $stream = new Stream(
                'https://example.com/stream.flv',
                'flv',
                Quality::HD1
            );

            expect($stream->isHls())->toBeFalse();
            expect($stream->isFlv())->toBeTrue();
        });
    });

    describe('Immutability', function () {
        test('should be immutable after creation', function () {
            $stream = new Stream(
                'https://example.com/stream.m3u8',
                'hls',
                Quality::HD1
            );

            // Stream对象应该是不可变的，没有setter方法
            expect(method_exists($stream, 'setUrl'))->toBeFalse();
            expect(method_exists($stream, 'setType'))->toBeFalse();
            expect(method_exists($stream, 'setQuality'))->toBeFalse();
        });
    });
});
