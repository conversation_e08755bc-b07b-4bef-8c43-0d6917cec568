<?php

declare(strict_types=1);

use LiveStream\Platforms\Douyin\Live;
use LiveStream\Enum\Quality;
use LiveStream\Enum\StreamType;
use Carbon\Carbon;

describe('Douyin Live Class', function () {

    beforeEach(function () {
        // 使用一个未来的时间戳（当前时间 + 2小时）
        $futureTimestamp = Carbon::now()->addHours(2)->timestamp;

        // 模拟带有 expire 参数的流 URL 数据
        $this->streamUrlWithExpire = [
            StreamType::FLV->value => [
                Quality::ORIGIN->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_or4.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=9eec3fe889997888c49871d9339148f4&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_or4&codec=h264",
                Quality::FULL_HD1->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_or4.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=9eec3fe889997888c49871d9339148f4&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_or4",
                Quality::HD1->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_hd.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=9f0043fb6a9ed9fade3fd4bcdadf9ad0&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_hd",
                Quality::SD1->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_ld.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=02281f1629ecf0cf9f2bc99b8907ba6d&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_ld",
                Quality::SD2->value => "http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_sd.flv?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=596ed9e474d3370db8fd5fc4a72f73aa&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_sd",
            ],
            StreamType::HLS->value => [
                Quality::ORIGIN->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_or4.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=63015841b626f417b3737b61ec815530&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&codec=h264",
                Quality::FULL_HD1->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_or4.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=63015841b626f417b3737b61ec815530&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X",
                Quality::HD1->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_hd.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=e4d2d1010515a2da16869b9735d4118a&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X",
                Quality::SD1->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_ld.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=ea1233511a78b0b4c99a2c97aa71ad66&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X",
                Quality::SD2->value => "http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_sd.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire={$futureTimestamp}&major_anchor_level=common&sign=b9a0ec5298bf87f09eb8601606106f73&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X",
            ],
        ];

        // 模拟没有 expire 参数的流 URL 数据
        $this->streamUrlWithoutExpire = [
            StreamType::FLV->value => [
                Quality::ORIGIN->value => 'http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_or4.flv?arch_hrchy=w1&exp_hrchy=w1&major_anchor_level=common&sign=9eec3fe889997888c49871d9339148f4&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_or4&codec=h264',
                Quality::HD1->value => 'http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_hd.flv?arch_hrchy=w1&exp_hrchy=w1&major_anchor_level=common&sign=9f0043fb6a9ed9fade3fd4bcdadf9ad0&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_hd',
            ],
            StreamType::HLS->value => [
                Quality::ORIGIN->value => 'http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_or4.m3u8?arch_hrchy=w1&exp_hrchy=w1&major_anchor_level=common&sign=63015841b626f417b3737b61ec815530&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&codec=h264',
                Quality::HD1->value => 'http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_hd.m3u8?arch_hrchy=w1&exp_hrchy=w1&major_anchor_level=common&sign=e4d2d1010515a2da16869b9735d4118a&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X',
            ],
        ];

        // 模拟带有无效 expire 参数的流 URL 数据
        $this->streamUrlWithInvalidExpire = [
            StreamType::FLV->value => [
                Quality::ORIGIN->value => 'http://pull-flv-l11.douyincdn.com/third/stream-694230885870862812_or4.flv?arch_hrchy=w1&exp_hrchy=w1&expire=invalid&major_anchor_level=common&sign=9eec3fe889997888c49871d9339148f4&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&unique_id=stream-694230885870862812_476_flv_or4&codec=h264',
            ],
            StreamType::HLS->value => [
                Quality::ORIGIN->value => 'http://pull-hls-l11.douyincdn.com/third/stream-694230885870862812_or4.m3u8?arch_hrchy=w1&exp_hrchy=w1&expire=-123&major_anchor_level=common&sign=63015841b626f417b3737b61ec815530&t_id=037-2025081120254510A48334857E66CDC1B9-9zL49X&codec=h264',
            ],
        ];

        // 使用一个未来的时间戳（当前时间 + 2小时）
        $this->expectedExpireTimestamp = $futureTimestamp;
        $this->expectedExpireCarbon = Carbon::createFromTimestamp($this->expectedExpireTimestamp);
    });

    describe('Expire Parameter Parsing', function () {
        test('should parse expire parameter from FLV URL with specific quality', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithExpire,
                '1000'
            );

            $stream = $live->getFlvUrl(Quality::ORIGIN);

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($this->expectedExpireTimestamp);
            expect($stream->isExpired())->toBeFalse();
        });

        test('should parse expire parameter from HLS URL with specific quality', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithExpire,
                '1000'
            );

            $stream = $live->getHlsUrl(Quality::HD1);

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($this->expectedExpireTimestamp);
            expect($stream->isExpired())->toBeFalse();
        });

        test('should parse expire parameter when using smart degradation for FLV', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithExpire,
                '1000'
            );

            $stream = $live->getFlvUrl(null); // 使用智能降级

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($this->expectedExpireTimestamp);
        });

        test('should parse expire parameter when using smart degradation for HLS', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithExpire,
                '1000'
            );

            $stream = $live->getHlsUrl(null); // 使用智能降级

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($this->expectedExpireTimestamp);
        });
    });

    describe('URLs Without Expire Parameter', function () {
        test('should handle FLV URLs without expire parameter', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithoutExpire,
                '1000'
            );

            $stream = $live->getFlvUrl(Quality::ORIGIN);

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeNull();
            expect($stream->isExpired())->toBeFalse();
            expect($stream->getTimeToExpire())->toBeNull();
        });

        test('should handle HLS URLs without expire parameter', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithoutExpire,
                '1000'
            );

            $stream = $live->getHlsUrl(Quality::HD1);

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeNull();
            expect($stream->isExpired())->toBeFalse();
            expect($stream->getTimeToExpire())->toBeNull();
        });
    });

    describe('Invalid Expire Parameter Handling', function () {
        test('should handle invalid expire parameter gracefully', function () {
            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $this->streamUrlWithInvalidExpire,
                '1000'
            );

            $flvStream = $live->getFlvUrl(Quality::ORIGIN);
            $hlsStream = $live->getHlsUrl(Quality::ORIGIN);

            expect($flvStream)->not->toBeNull();
            expect($flvStream->getExpire())->toBeNull();

            expect($hlsStream)->not->toBeNull();
            expect($hlsStream->getExpire())->toBeNull();
        });
    });

    describe('URL Parsing Edge Cases', function () {
        test('should handle URLs with multiple query parameters', function () {
            $urlWithMultipleParams = [
                StreamType::FLV->value => [
                    Quality::ORIGIN->value => "http://example.com/stream.flv?param1=value1&expire={$this->expectedExpireTimestamp}&param2=value2&param3=value3",
                ],
            ];

            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $urlWithMultipleParams,
                '1000'
            );

            $stream = $live->getFlvUrl(Quality::ORIGIN);

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($this->expectedExpireTimestamp);
        });

        test('should handle URLs with expire parameter at different positions', function () {
            $urlWithExpireAtStart = [
                StreamType::HLS->value => [
                    Quality::ORIGIN->value => "http://example.com/stream.m3u8?expire={$this->expectedExpireTimestamp}&other_param=value",
                ],
            ];

            $live = new Live(
                2,
                'Test Title',
                'Test Anchor',
                '123',
                $urlWithExpireAtStart,
                '1000'
            );

            $stream = $live->getHlsUrl(Quality::ORIGIN);

            expect($stream)->not->toBeNull();
            expect($stream->getExpire())->toBeInstanceOf(Carbon::class);
            expect($stream->getExpire()->timestamp)->toBe($this->expectedExpireTimestamp);
        });
    });
});
